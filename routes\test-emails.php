<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Mail;
use App\Notifications\ReservationConfirmedNotification;
use App\Notifications\TestReservationConfirmedNotification;
use App\Notifications\CustomerReservationConfirmedNotification;
use App\Notifications\CustomerTestReservationConfirmedNotification;
use App\Notifications\NewContactNotification;
use App\Notifications\ContactReplyNotification;

/*
|--------------------------------------------------------------------------
| Test Email Routes
|--------------------------------------------------------------------------
|
| Ces routes sont uniquement pour tester les templates d'email.
| À supprimer en production.
|
*/

Route::get('/test-emails/reservation', function () {
    // Créer des données de test
    $reservation = (object) [
        'id' => 123,
        'date_reservation' => now(),
        'statut' => 'confirmé',
        'methode_paiement' => 'carte_bancaire',
        'date_paiement' => now(),
        'transaction_id' => 'TXN_123456789',
    ];

    $user = (object) [
        'nom' => 'Dupont',
        'prenom' => 'Jean',
        'email' => '<EMAIL>',
        'telephone' => '06 12 34 56 78',
    ];

    $stage = (object) [
        'date_debut' => now()->addDays(7),
        'prix' => 259.00,
        'lieu' => (object) [
            'nom' => 'Centre de Formation Paris',
            'adresse' => '123 Rue de la Paix',
            'ville' => (object) [
                'nom' => 'Paris',
                'code_postal' => '75001',
            ],
        ],
    ];

    $typeStage = (object) [
        'nom' => 'Stage de récupération de points - 2 jours',
    ];

    // Créer une instance factice avec les données
    $fakeReservation = (object) [
        'id' => $reservation->id,
        'date_reservation' => $reservation->date_reservation,
        'statut' => $reservation->statut,
        'methode_paiement' => $reservation->methode_paiement,
        'date_paiement' => $reservation->date_paiement,
        'transaction_id' => $reservation->transaction_id,
        'user' => $user,
        'stage' => $stage,
        'typeStage' => $typeStage,
    ];

    // Retourner la vue directement pour prévisualisation
    return view('emails.reservation-confirmed', [
        'reservation' => $fakeReservation,
        'user' => $user,
        'stage' => $stage,
        'typeStage' => $typeStage,
    ]);
});

Route::get('/test-emails/test-reservation', function () {
    // Créer des données de test pour le test psychotechnique
    $reservation = (object) [
        'id' => 456,
        'date_reservation' => now(),
        'statut' => 'confirmé',
        'methode_paiement' => 'virement',
        'date_paiement' => now(),
        'motif' => 'Suspension de permis',
        'transaction_id' => 'TXN_987654321',
    ];

    $user = (object) [
        'nom' => 'Martin',
        'prenom' => 'Marie',
        'email' => '<EMAIL>',
        'telephone' => '06 98 76 54 32',
    ];

    $testPsycho = (object) [
        'date' => now()->addDays(5),
        'prix' => 120.00,
        'lieu' => (object) [
            'nom' => 'Cabinet Psychotechnique Lyon',
            'adresse' => '456 Avenue de la République',
            'ville' => (object) [
                'nom' => 'Lyon',
                'code_postal' => '69002',
            ],
        ],
    ];

    $typeTest = (object) [
        'nom' => 'Test psychotechnique standard',
    ];

    // Créer une instance factice avec les données
    $fakeReservation = (object) [
        'id' => $reservation->id,
        'date_reservation' => $reservation->date_reservation,
        'statut' => $reservation->statut,
        'methode_paiement' => $reservation->methode_paiement,
        'date_paiement' => $reservation->date_paiement,
        'motif' => $reservation->motif,
        'transaction_id' => $reservation->transaction_id,
        'user' => $user,
        'testPsycho' => $testPsycho,
        'typeTestPsycho' => $typeTest,
    ];

    return view('emails.test-reservation-confirmed', [
        'reservation' => $fakeReservation,
        'user' => $user,
        'testPsycho' => $testPsycho,
        'typeTest' => $typeTest,
    ]);
});

Route::get('/test-emails/new-contact', function () {
    $contact = (object) [
        'id' => 789,
        'nom' => 'Durand',
        'prenom' => 'Pierre',
        'email' => '<EMAIL>',
        'telephone' => '06 11 22 33 44',
        'sujet' => 'stage',
        'message' => 'Bonjour, je souhaiterais connaître les prochaines dates disponibles pour un stage de récupération de points dans la région parisienne. Merci.',
    ];

    $sujetTexte = 'Stage de récupération de points';

    return view('emails.new-contact', [
        'contact' => $contact,
        'sujetTexte' => $sujetTexte,
    ]);
});

Route::get('/test-emails/contact-reply', function () {
    $contact = (object) [
        'id' => 789,
        'nom' => 'Durand',
        'prenom' => 'Pierre',
        'email' => '<EMAIL>',
        'sujet' => 'stage',
        'message' => 'Bonjour, je souhaiterais connaître les prochaines dates disponibles pour un stage de récupération de points dans la région parisienne. Merci.',
        'reponse' => 'Bonjour Pierre, merci pour votre message. Nous avons plusieurs dates disponibles en région parisienne : 15/12/2024, 22/12/2024 et 05/01/2025. Vous pouvez réserver directement sur notre site ou nous appeler au 01 23 45 67 89. Cordialement, l\'équipe Stage Permis.',
    ];

    $sujetTexte = 'Stage de récupération de points';

    return view('emails.contact-reply', [
        'contact' => $contact,
        'sujetTexte' => $sujetTexte,
    ]);
});

// Customer notification templates
Route::get('/test-emails/customer-reservation-confirmed', function () {
    $reservation = \App\Models\Reservation::with(['stage.lieu.ville.departement', 'user', 'typeStage'])
        ->where('statut', 'confirmée')
        ->first();

    if (!$reservation) {
        return 'No confirmed stage reservations found in database';
    }

    return view('emails.customer-reservation-confirmed', [
        'reservation' => $reservation,
        'user' => $reservation->user,
        'stage' => $reservation->stage,
        'typeStage' => $reservation->typeStage,
    ]);
});

Route::get('/test-emails/customer-test-reservation-confirmed', function () {
    $reservation = \App\Models\ReservationTestPsycho::with(['testPsycho.lieu.ville.departement', 'user', 'typeTestPsycho'])
        ->where('statut', 'confirmée')
        ->first();

    if (!$reservation) {
        return 'No confirmed test reservations found in database';
    }

    return view('emails.customer-test-reservation-confirmed', [
        'reservation' => $reservation,
        'user' => $reservation->user,
        'testPsycho' => $reservation->testPsycho,
        'typeTest' => $reservation->typeTestPsycho,
    ]);
});
