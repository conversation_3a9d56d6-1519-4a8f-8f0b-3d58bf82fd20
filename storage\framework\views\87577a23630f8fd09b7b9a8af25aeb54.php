<?php $__env->startSection('title', 'Confirmation de votre réservation - Stage de récupération de points'); ?>

<?php $__env->startSection('header-title', 'Réservation Confirmée'); ?>
<?php $__env->startSection('header-subtitle', 'Votre stage de récupération de points est confirmé'); ?>

<?php $__env->startSection('content'); ?>
    <div class="greeting">
        Bonjour <?php echo e($user->prenom); ?> <?php echo e($user->nom); ?>,
    </div>
    
    <p style="font-size: 16px; margin-bottom: 25px; color: #495057;">
        Nous avons le plaisir de vous confirmer votre réservation pour le stage de récupération de points. 
        Voici les détails de votre réservation :
    </p>
    
    <div class="highlight-box">
        <div class="amount">Réservation #<?php echo e($reservation->id); ?></div>
        <div><?php echo e($typeStage->nom ?? 'Stage de récupération de points'); ?></div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">📋 Détails de votre réservation</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Numéro de réservation :</span>
                <span class="info-value"><strong>#<?php echo e($reservation->id); ?></strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">Type de stage :</span>
                <span class="info-value"><?php echo e($typeStage->nom ?? 'Non spécifié'); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Date de réservation :</span>
                <span class="info-value"><?php echo e($reservation->date_reservation->format('d/m/Y à H:i')); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Statut :</span>
                <span class="info-value">
                    <span class="status-badge status-<?php echo e(strtolower($reservation->statut)); ?>">
                        <?php echo e(ucfirst($reservation->statut)); ?>

                    </span>
                </span>
            </div>
            <?php if($reservation->methode_paiement): ?>
            <div class="info-row">
                <span class="info-label">Méthode de paiement :</span>
                <span class="info-value"><?php echo e(ucfirst(str_replace('_', ' ', $reservation->methode_paiement))); ?></span>
            </div>
            <?php endif; ?>
            <?php if($reservation->date_paiement): ?>
            <div class="info-row">
                <span class="info-label">Date de paiement :</span>
                <span class="info-value"><?php echo e($reservation->date_paiement->format('d/m/Y à H:i')); ?></span>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">📅 Informations sur le stage</h2>
        <div class="info-grid">
            <?php if($stage): ?>
                <div class="info-row">
                    <span class="info-label">Date de début :</span>
                    <span class="info-value"><strong><?php echo e($stage->date_debut->format('d/m/Y')); ?></strong></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Date de fin :</span>
                    <span class="info-value"><?php echo e($stage->date_fin->format('d/m/Y')); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Prix :</span>
                    <span class="info-value"><strong><?php echo e(number_format($stage->prix, 2, ',', ' ')); ?> €</strong></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Référence :</span>
                    <span class="info-value"><?php echo e($stage->reference); ?></span>
                </div>
                <?php if($stage->lieu): ?>
                <div class="info-row">
                    <span class="info-label">Lieu :</span>
                    <span class="info-value"><?php echo e($stage->lieu->nom); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Adresse :</span>
                    <span class="info-value"><?php echo e($stage->lieu->adresse); ?></span>
                </div>
                <?php if($stage->lieu->ville): ?>
                <div class="info-row">
                    <span class="info-label">Ville :</span>
                    <span class="info-value"><?php echo e($stage->lieu->ville->nom); ?> (<?php echo e($stage->lieu->ville->code_postal); ?>)</span>
                </div>
                <?php endif; ?>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">👤 Vos informations</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Nom :</span>
                <span class="info-value"><?php echo e($user->nom); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Prénom :</span>
                <span class="info-value"><?php echo e($user->prenom); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Email :</span>
                <span class="info-value"><?php echo e($user->email); ?></span>
            </div>
            <?php if($user->telephone): ?>
            <div class="info-row">
                <span class="info-label">Téléphone :</span>
                <span class="info-value"><?php echo e($user->telephone); ?></span>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="divider"></div>
    
    <div style="background-color: #e3f2fd; border-left: 4px solid #2196f3; padding: 20px; margin: 20px 0; border-radius: 0 8px 8px 0;">
        <h3 style="color: #1976d2; margin-bottom: 10px;">📝 Informations importantes</h3>
        <ul style="margin: 0; padding-left: 20px; color: #495057;">
            <li style="margin-bottom: 8px;">Veuillez vous présenter 15 minutes avant le début du stage</li>
            <li style="margin-bottom: 8px;">Munissez-vous d'une pièce d'identité en cours de validité</li>
            <li style="margin-bottom: 8px;">Apportez votre permis de conduire</li>
            <li style="margin-bottom: 8px;">En cas d'empêchement, contactez-nous au plus tôt</li>
        </ul>
    </div>
    
    <p style="font-size: 16px; color: #495057; text-align: center; margin-top: 30px;">
        Nous vous remercions de votre confiance et vous souhaitons un excellent stage !
    </p>
    
    <p style="font-size: 14px; color: #6c757d; text-align: center; margin-top: 20px;">
        Si vous avez des questions, n'hésitez pas à nous contacter.
    </p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\stage-permis\resources\views/emails/customer-reservation-confirmed.blade.php ENDPATH**/ ?>