import DataTable from '@/components/DataTable';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { UserInfo } from '@/components/user-info';
import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem, PageProps, PaginatedData, ReservationTestPsycho, TestPsycho, TypeTestPsycho, User } from '@/types';
import { Head, router, useForm } from '@inertiajs/react';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useState } from 'react';
import { toast } from 'sonner';
import { CreditCard, Calendar, Hash } from 'lucide-react';

interface ReservationsTestsPsychosPageProps extends PageProps {
  reservations: PaginatedData<ReservationTestPsycho>;
  tests: TestPsycho[];
  typeTests: TypeTestPsycho[];
  users: User[];
}

export default function Index({ reservations, tests, typeTests, users }: ReservationsTestsPsychosPageProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isImportOpen, setIsImportOpen] = useState(false);
  const [editingReservation, setEditingReservation] = useState<ReservationTestPsycho | null>(null);

  const form = useForm({
    test_psycho_id: '',
    user_id: '',
    type_test_psycho_id: '',
    date_reservation: '',
    statut: 'en attente',
    motif: '',
    permis_recto: '',
    permis_verso: '',
    document_tribunal: '',
    // Ajout des champs pour les fichiers
    permis_recto_file: null as File | null,
    permis_verso_file: null as File | null,
    document_tribunal_file: null as File | null,
    // Ajout des champs de paiement
    date_paiement: '',
    methode_paiement: '',
    transaction_id: '',
  });

  const importForm = useForm({
    file: null as File | null,
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmée':
        return <Badge className="bg-green-500">{status}</Badge>;
      case 'en attente':
        return <Badge className="bg-yellow-500">{status}</Badge>;
      case 'annulée':
        return <Badge className="bg-red-500">{status}</Badge>;
      default:
        return status;
    }
  };

  const columns = [
    {
      key: 'user',
      index: 'user_id',
      label: 'Client',
      render: (_value: unknown, row: Record<string, unknown>) => {
        const reservation = row as unknown as ReservationTestPsycho;
        return reservation.user ? (
          <div className="flex min-w-45 items-center gap-2">
            <UserInfo user={reservation.user} showEmail={true} />
          </div>
        ) : (
          ''
        );
      },
    },
    {
      key: 'test_psycho',
      index: 'test_psycho_id',
      label: 'Test Psychotechnique',
      render: (_value: unknown, row: Record<string, unknown>) => {
        const reservation = row as unknown as ReservationTestPsycho;
        return reservation.test_psycho ? (
          <div className="flex min-w-45 items-center gap-2">
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span title={reservation.type_test_psycho?.nom} className="truncate font-medium">
                {reservation.test_psycho.reference}
              </span>
              <span
                title={reservation.test_psycho.lieu?.nom + '-' + format(parseISO(reservation.test_psycho.date), 'dd/MM/yyyy', { locale: fr })}
                className="text-muted-foreground truncate text-xs"
              >
                {reservation.test_psycho.lieu?.nom} - {format(parseISO(reservation.test_psycho.date), 'dd/MM/yyyy', { locale: fr })}
              </span>
            </div>
          </div>
        ) : (
          ''
        );
      },
    },
    {
      key: 'type_test_psycho',
      index: 'type_test_psycho_id',
      label: 'Type de test',
      render: (_value: unknown, row: Record<string, unknown>) => {
        const reservation = row as unknown as ReservationTestPsycho;
        return reservation.type_test_psycho?.nom ? (
          <div className="flex min-w-45 items-center gap-2">
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span title={reservation.type_test_psycho?.nom} className="truncate font-medium">
                {reservation.type_test_psycho?.nom}
              </span>
            </div>
          </div>
        ) : (
          ''
        );
      },
    },
    {
      key: 'date_reservation',
      label: 'Date de réservation',
      render: (value: unknown) => {
        if (typeof value === 'string') {
          return format(parseISO(value), 'dd/MM/yyyy HH:mm', { locale: fr });
        }
        return '';
      },
    },
    {
      key: 'statut',
      label: 'Statut',
      render: (value: unknown) => {
        return getStatusBadge(value as string);
      },
    },
    {
      key: 'payment_details',
      label: 'Détails de paiement',
      render: (_value: unknown, row: Record<string, unknown>) => {
        const reservation = row as unknown as ReservationTestPsycho;

        // Only show payment details if payment information exists
        if (!reservation.date_paiement && !reservation.methode_paiement && !reservation.transaction_id) {
          return <span className="text-muted-foreground text-sm">Aucun paiement</span>;
        }

        return (
          <div className="flex min-w-45 items-center gap-2">
            <div className="grid flex-1 text-left text-sm leading-tight">
              {reservation.methode_paiement && (
                <div className="flex items-center gap-1">
                  <CreditCard className="h-3 w-3" />
                  <span className="truncate font-medium">{reservation.methode_paiement}</span>
                </div>
              )}
              {reservation.date_paiement && (
                <div className="flex items-center gap-1 text-muted-foreground text-xs">
                  <Calendar className="h-3 w-3" />
                  <span className="truncate">
                    {format(parseISO(reservation.date_paiement), 'dd/MM/yyyy HH:mm', { locale: fr })}
                  </span>
                </div>
              )}
              {reservation.transaction_id && (
                <div className="flex items-center gap-1 text-muted-foreground text-xs">
                  <Hash className="h-3 w-3" />
                  <span className="truncate" title={reservation.transaction_id}>
                    {reservation.transaction_id.length > 15
                      ? `${reservation.transaction_id.substring(0, 15)}...`
                      : reservation.transaction_id}
                  </span>
                </div>
              )}
            </div>
          </div>
        );
      },
    },
  ];

  const handleAdd = () => {
    setEditingReservation(null);
    form.reset();
    form.setData('statut', 'en attente');
    setIsOpen(true);
  };

  const handleImport = () => {
    importForm.reset();
    setIsImportOpen(true);
  };

  const handleImportSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    importForm.post(route('admin.reservations-tests-psychos.import'), {
      forceFormData: true,
      onSuccess: () => {
        setIsImportOpen(false);
        // toast.success('Réservations importées avec succès');
      },
      onError: (errors) => {
        console.log(errors);
        toast.error("Erreur lors de l'importation : " + (errors.file || errors.message || 'Erreur inconnue'));
      },
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      importForm.setData('file', e.target.files[0]);
    }
  };

  const handleEdit = (row: Record<string, unknown>) => {
    const reservation = row as unknown as ReservationTestPsycho;
    setEditingReservation(reservation);

    // Vérifier que les IDs existent et les convertir en chaînes de caractères
    const test_psycho_id = reservation.test_psycho_id !== undefined ? reservation.test_psycho_id.toString() : '';
    const user_id = reservation.user_id !== undefined ? reservation.user_id.toString() : '';
    const type_test_psycho_id = reservation.type_test_psycho_id !== undefined ? reservation.type_test_psycho_id.toString() : '';

    // Formater la date si elle existe
    const formattedDateReservation = reservation.date_reservation ? format(parseISO(reservation.date_reservation), 'yyyy-MM-dd') : '';

    // Formater la date de paiement si elle existe
    const formattedDatePaiement = reservation.date_paiement ? format(parseISO(reservation.date_paiement), 'yyyy-MM-dd') : '';

    form.setData({
      test_psycho_id: test_psycho_id,
      user_id: user_id,
      type_test_psycho_id: type_test_psycho_id,
      date_reservation: formattedDateReservation,
      statut: reservation.statut || 'en attente',
      motif: reservation.motif || '',
      permis_recto: reservation.permis_recto || '',
      permis_verso: reservation.permis_verso || '',
      document_tribunal: reservation.document_tribunal || '',
      // Initialiser les champs de fichiers à null
      permis_recto_file: null,
      permis_verso_file: null,
      document_tribunal_file: null,
      // Ajouter les champs de paiement
      date_paiement: formattedDatePaiement,
      methode_paiement: reservation.methode_paiement || '',
      transaction_id: reservation.transaction_id || '',
    });

    setIsOpen(true);
  };

  const handleDelete = (row: Record<string, unknown>) => {
    const reservation = row as unknown as ReservationTestPsycho;
    if (confirm(`Êtes-vous sûr de vouloir supprimer cette réservation ?`)) {
      router.delete(`/admin/reservations-tests-psychos/${reservation.id}`);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
        console.log(form)

    if (editingReservation) {
      form.post(`/admin/reservations-tests-psychos/${editingReservation.id}`, {
        forceFormData: true,
        onSuccess: () => {
          setIsOpen(false);
        },
      });
    } else {
      form.post('/admin/reservations-tests-psychos', {
        forceFormData: true,
        onSuccess: () => {
          setIsOpen(false);
        },
      });
    }
  };

  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Réservations de tests psychotechniques',
      href: '/admin/reservations-tests-psychos',
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Réservations de tests psychotechniques" />
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <DataTable
          title="Réservations de tests psychotechniques"
          columns={columns}
          data={reservations.data.map((reservation) => ({
            id: reservation.id,
            user: reservation.user,
            test_psycho: reservation.test_psycho,
            type_test_psycho: reservation.type_test_psycho,
            date_reservation: reservation.date_reservation,
            statut: reservation.statut,
            // Ajouter les IDs nécessaires pour l'édition
            test_psycho_id: reservation.test_psycho_id,
            user_id: reservation.user_id,
            type_test_psycho_id: reservation.type_test_psycho_id,
            // Ajouter les autres champs qui pourraient être nécessaires
            motif: reservation.motif,
            permis_recto: reservation.permis_recto,
            permis_verso: reservation.permis_verso,
            document_tribunal: reservation.document_tribunal,
            // Ajouter les champs de paiement
            date_paiement: reservation.date_paiement,
            methode_paiement: reservation.methode_paiement,
            transaction_id: reservation.transaction_id,
          }))}
          onAdd={handleAdd}
          onImport={handleImport}
          onEdit={handleEdit}
          onDelete={handleDelete}
          pagination={{
            links: reservations.links,
            from: reservations.from,
            to: reservations.to,
            total: reservations.total,
          }}
        />
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[600px] md:max-w-[700px] lg:max-w-[900px]">
          <DialogHeader>
            <DialogTitle>{editingReservation ? 'Modifier la réservation' : 'Ajouter une réservation'}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="test_psycho_id" className="text-sm font-medium">
                  Test psychotechnique
                </label>
                <Select value={form.data.test_psycho_id} onValueChange={(value) => form.setData('test_psycho_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un test" />
                  </SelectTrigger>
                  <SelectContent>
                    {tests.map((test) => (
                      <SelectItem key={test.id} value={test.id.toString()}>
                        {test.reference} - {format(parseISO(test.date), 'dd/MM/yyyy', { locale: fr })} - {test.lieu?.nom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.errors.test_psycho_id && <p className="text-sm text-red-500">{form.errors.test_psycho_id}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="user_id" className="text-sm font-medium">
                  Client
                </label>
                <Select value={form.data.user_id} onValueChange={(value) => form.setData('user_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un client" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.nom} {user.prenom} ({user.email})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.errors.user_id && <p className="text-sm text-red-500">{form.errors.user_id}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="type_test_psycho_id" className="text-sm font-medium">
                  Type de test
                </label>
                <Select value={form.data.type_test_psycho_id} onValueChange={(value) => form.setData('type_test_psycho_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un type de test" />
                  </SelectTrigger>
                  <SelectContent>
                    {typeTests.map((type) => (
                      <SelectItem key={type.id} value={type.id.toString()}>
                        {type.nom}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.errors.type_test_psycho_id && <p className="text-sm text-red-500">{form.errors.type_test_psycho_id}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="date_reservation" className="text-sm font-medium">
                  Date de réservation
                </label>
                <Input
                  id="date_reservation"
                  type="date"
                  value={form.data.date_reservation}
                  onChange={(e) => form.setData('date_reservation', e.target.value)}
                  required
                />
                {form.errors.date_reservation && <p className="text-sm text-red-500">{form.errors.date_reservation}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="statut" className="text-sm font-medium">
                  Statut
                </label>
                <Select value={form.data.statut} onValueChange={(value) => form.setData('statut', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="confirmée">Confirmée</SelectItem>
                    <SelectItem value="en attente">En attente</SelectItem>
                    <SelectItem value="annulée">Annulée</SelectItem>
                  </SelectContent>
                </Select>
                {form.errors.statut && <p className="text-sm text-red-500">{form.errors.statut}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="motif" className="text-sm font-medium">
                  Motif
                </label>
                <Textarea id="motif" value={form.data.motif} onChange={(e) => form.setData('motif', e.target.value)} />
                {form.errors.motif && <p className="text-sm text-red-500">{form.errors.motif}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="date_paiement" className="text-sm font-medium">
                  Date de paiement
                </label>
                <Input
                  id="date_paiement"
                  type="date"
                  value={form.data.date_paiement}
                  onChange={(e) => form.setData('date_paiement', e.target.value)}
                />
                {form.errors.date_paiement && <p className="text-sm text-red-500">{form.errors.date_paiement}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="methode_paiement" className="text-sm font-medium">
                  Méthode de paiement
                </label>
                <Input
                  id="methode_paiement"
                  value={form.data.methode_paiement}
                  onChange={(e) => form.setData('methode_paiement', e.target.value)}
                  placeholder="Ex: Carte bancaire, Espèces, Virement..."
                />
                {form.errors.methode_paiement && <p className="text-sm text-red-500">{form.errors.methode_paiement}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="transaction_id" className="text-sm font-medium">
                  ID de transaction
                </label>
                <Input
                  id="transaction_id"
                  value={form.data.transaction_id}
                  onChange={(e) => form.setData('transaction_id', e.target.value)}
                  placeholder="Identifiant de la transaction"
                />
                {form.errors.transaction_id && <p className="text-sm text-red-500">{form.errors.transaction_id}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="permis_recto" className="text-sm font-medium">
                  Permis (recto)
                </label>
                <div className="flex flex-col space-y-2">
                  {form.data.permis_recto && (
                    <div className="flex items-center space-x-2">
                      <a href={`/files/${form.data.permis_recto}`} target="_blank" className="flex items-center text-blue-600 hover:underline">
                        <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                          />
                        </svg>
                        Télécharger le fichier existant
                      </a>
                    </div>
                  )}
                  <Input
                    id="permis_recto_file"
                    type="file"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        form.setData('permis_recto_file', e.target.files[0]);
                      }
                    }}
                  />
                  <Input
                    id="permis_recto"
                    value={form.data.permis_recto || ''}
                    onChange={(e) => form.setData('permis_recto', e.target.value)}
                    placeholder="Chemin du fichier (se remplit automatiquement après upload)"
                    className="mt-2"
                  />
                </div>
                {form.errors.permis_recto && <p className="text-sm text-red-500">{form.errors.permis_recto}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="permis_verso" className="text-sm font-medium">
                  Permis (verso)
                </label>
                <div className="flex flex-col space-y-2">
                  {form.data.permis_verso && (
                    <div className="flex items-center space-x-2">
                      <a href={`/files/${form.data.permis_verso}`} target="_blank" className="flex items-center text-blue-600 hover:underline">
                        <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                          />
                        </svg>
                        Télécharger le fichier existant
                      </a>
                    </div>
                  )}
                  <Input
                    id="permis_verso_file"
                    type="file"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        form.setData('permis_verso_file', e.target.files[0]);
                      }
                    }}
                  />
                  <Input
                    id="permis_verso"
                    value={form.data.permis_verso || ''}
                    onChange={(e) => form.setData('permis_verso', e.target.value)}
                    placeholder="Chemin du fichier (se remplit automatiquement après upload)"
                    className="mt-2"
                  />
                </div>
                {form.errors.permis_verso && <p className="text-sm text-red-500">{form.errors.permis_verso}</p>}
              </div>
              <div className="space-y-2">
                <label htmlFor="document_tribunal" className="text-sm font-medium">
                  Document tribunal
                </label>
                <div className="flex flex-col space-y-2">
                  {form.data.document_tribunal && (
                    <div className="flex items-center space-x-2">
                      <a href={`/files/${form.data.document_tribunal}`} target="_blank" className="flex items-center text-blue-600 hover:underline">
                        <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                          />
                        </svg>
                        Télécharger le fichier existant
                      </a>
                    </div>
                  )}
                  <Input
                    id="document_tribunal_file"
                    type="file"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        form.setData('document_tribunal_file', e.target.files[0]);
                      }
                    }}
                  />
                  <Input
                    id="document_tribunal"
                    value={form.data.document_tribunal || ''}
                    onChange={(e) => form.setData('document_tribunal', e.target.value)}
                    placeholder="Chemin du fichier (se remplit automatiquement après upload)"
                    className="mt-2"
                  />
                </div>
                {form.errors.document_tribunal && <p className="text-sm text-red-500">{form.errors.document_tribunal}</p>}
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                Annuler
              </Button>
              <Button type="submit" disabled={form.processing}>
                {editingReservation ? 'Mettre à jour' : 'Ajouter'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={isImportOpen} onOpenChange={setIsImportOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Importer des réservations de tests psychotechniques</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleImportSubmit} className="space-y-4">
            <div>
              <Label htmlFor="file">Fichier Excel</Label>
              <Input id="file" type="file" accept=".xlsx,.xls" onChange={handleFileChange} className="mt-1" />
              {importForm.errors.file && <p className="mt-1 text-sm text-red-500">{importForm.errors.file}</p>}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p>Le fichier doit contenir les colonnes suivantes :</p>
              <ul className="mt-2 list-disc pl-5">
                <li>nom (obligatoire)</li>
                <li>prenom (obligatoire)</li>
                <li>email (obligatoire)</li>
                <li>test_id (obligatoire)</li>
                <li>type_test_id (obligatoire)</li>
                <li>date_reservation (optionnel, format: DD/MM/YYYY)</li>
                <li>statut (optionnel, valeurs: confirmée, en attente, annulée)</li>
                <li>methode_paiement (optionnel)</li>
                <li>civilite (optionnel)</li>
                <li>adresse (optionnel)</li>
                <li>cp (optionnel)</li>
                <li>ville (optionnel)</li>
                <li>date_naissance (optionnel, format: DD/MM/YYYY)</li>
                <li>lieu_naissance (optionnel)</li>
                <li>mobile (optionnel)</li>
                <li>tel (optionnel)</li>
                <li>num_permis (optionnel)</li>
                <li>date_permis (optionnel, format: DD/MM/YYYY)</li>
                <li>lieu_permis (optionnel)</li>
                <li>password (optionnel, si non fourni un mot de passe aléatoire sera généré)</li>
              </ul>
              <p className="mt-2">
                <strong>Note:</strong> Si l'utilisateur existe déjà (même email), la réservation sera associée à cet utilisateur.
              </p>
              <p className="mt-2">
                <strong>Note:</strong> Les dates peuvent être au format texte (JJ/MM/AAAA) ou au format numérique Excel.
              </p>
            </div>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setIsImportOpen(false)}>
                Annuler
              </Button>
              <Button type="submit" disabled={importForm.processing || !importForm.data.file}>
                {importForm.processing ? 'Importation...' : 'Importer'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
