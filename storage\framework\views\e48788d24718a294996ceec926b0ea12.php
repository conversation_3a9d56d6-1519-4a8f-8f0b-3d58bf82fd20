<?php $__env->startSection('title', 'Confirmation de réservation - Stage de récupération de points'); ?>

<?php $__env->startSection('header-title', 'Nouvelle Réservation'); ?>
<?php $__env->startSection('header-subtitle', 'Stage de récupération de points confirmé'); ?>

<?php $__env->startSection('content'); ?>
    <div class="greeting">
        Bonjour,
    </div>
    
    <p style="font-size: 16px; margin-bottom: 25px; color: #495057;">
        Une nouvelle réservation de stage de récupération de points vient d'être ajoutée avec succès.
    </p>
    
    <div class="highlight-box">
        <div class="amount">Réservation #<?php echo e($reservation->id); ?></div>
        <div><?php echo e($typeStage->nom ?? 'Stage de récupération de points'); ?></div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">📋 Détails de la réservation</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Numéro de réservation :</span>
                <span class="info-value"><strong>#<?php echo e($reservation->id); ?></strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">Type de stage :</span>
                <span class="info-value"><?php echo e($typeStage->nom ?? 'Non spécifié'); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Date de réservation :</span>
                <span class="info-value"><?php echo e($reservation->date_reservation->format('d/m/Y à H:i')); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Statut :</span>
                <span class="info-value">
                    <span class="status-badge status-<?php echo e(strtolower($reservation->statut)); ?>">
                        <?php echo e(ucfirst($reservation->statut)); ?>

                    </span>
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">Méthode de paiement :</span>
                <span class="info-value"><?php echo e(ucfirst($reservation->methode_paiement)); ?></span>
            </div>
            <?php if($reservation->date_paiement): ?>
            <div class="info-row">
                <span class="info-label">Date de paiement :</span>
                <span class="info-value"><?php echo e($reservation->date_paiement->format('d/m/Y à H:i')); ?></span>
            </div>
            <?php endif; ?>
            <?php if($reservation->transaction_id): ?>
            <div class="info-row">
                <span class="info-label">ID de transaction :</span>
                <span class="info-value"><?php echo e($reservation->transaction_id); ?></span>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">👤 Informations du client</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Nom complet :</span>
                <span class="info-value"><?php echo e($user->nom); ?> <?php echo e($user->prenom); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Email :</span>
                <span class="info-value"><?php echo e($user->email); ?></span>
            </div>
            <?php if($user->telephone): ?>
            <div class="info-row">
                <span class="info-label">Téléphone :</span>
                <span class="info-value"><?php echo e($user->telephone); ?></span>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="content-section">
        <h2 class="section-title">📅 Détails du stage</h2>
        <div class="info-grid">
            <div class="info-row">
                <span class="info-label">Date du stage :</span>
                <span class="info-value"><strong><?php echo e($stage->date_debut->format('d/m/Y')); ?></strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">Prix :</span>
                <span class="info-value"><strong><?php echo e(number_format($stage->prix, 2, ',', ' ')); ?> €</strong></span>
            </div>
            <?php if($stage->lieu): ?>
                <div class="info-row">
                    <span class="info-label">Lieu :</span>
                    <span class="info-value"><?php echo e($stage->lieu->nom); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Adresse :</span>
                    <span class="info-value"><?php echo e($stage->lieu->adresse); ?></span>
                </div>
                <?php if($stage->lieu->ville): ?>
                <div class="info-row">
                    <span class="info-label">Ville :</span>
                    <span class="info-value"><?php echo e($stage->lieu->ville->nom); ?> (<?php echo e($stage->lieu->ville->code_postal); ?>)</span>
                </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="divider"></div>
    
    <div style="text-align: center;">
        <a href="<?php echo e(url('/admin/reservations/' . $reservation->id)); ?>" class="btn">
            Voir la réservation complète
        </a>
    </div>
    
    <p style="font-size: 14px; color: #6c757d; text-align: center; margin-top: 30px;">
        Cette notification a été générée automatiquement par le système de réservation.
    </p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\stage-permis\resources\views/emails/reservation-confirmed.blade.php ENDPATH**/ ?>